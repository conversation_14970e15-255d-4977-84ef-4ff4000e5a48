'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { NavLink } from '@/components/nav-link';

interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
}

export function NavSidebar() {
  const pathname = usePathname();
  const [lastProjectId, setLastProjectId] = useState<string | null>(null);

  // Extract project ID from pathname if it's a project detail page
  // Use a regex that matches UUID format or any string after /projects/
  const projectIdMatch = pathname.match(/\/projects\/([^\/]+)/);
  const currentProjectId = projectIdMatch ? projectIdMatch[1] : null;

  // Store the last visited project ID in localStorage and state
  useEffect(() => {
    if (currentProjectId) {
      setLastProjectId(currentProjectId);
      localStorage.setItem('lastProjectId', currentProjectId);
    } else {
      // Load from localStorage if no current project ID
      const stored = localStorage.getItem('lastProjectId');
      if (stored && !lastProjectId) {
        setLastProjectId(stored);
      }
    }
  }, [currentProjectId, lastProjectId]);

  // Use current project ID if available, otherwise use last visited project ID
  const projectId = currentProjectId || lastProjectId;

  console.log('Current pathname:', pathname);
  console.log('Current project ID:', currentProjectId);
  console.log('Last project ID:', lastProjectId);
  console.log('Using project ID:', projectId);

  const navItems: NavItem[] = [
    {
      name: 'Project Overview',
      href: projectId ? `/projects/${projectId}` : '/projects',
      icon: <HomeIcon />,
    },
    {
      name: 'Documents',
      href: '/documents',
      icon: <DocumentIcon />,
    },
    {
      name: 'Photos',
      href: '/photos',
      icon: <PhotoIcon />,
    },
    {
      name: 'Contacts',
      href: '/contacts',
      icon: <ContactIcon />,
    },
    {
      name: 'To-Do List',
      href: '/todo',
      icon: <TodoIcon />,
    },
    {
      name: 'Private Notes',
      href: '/notes',
      icon: <NoteIcon />,
    },
  ];

  return (
    <div className="w-60 bg-white border-r border-gray-200 h-screen pl-4 pr-2 py-4 text-gray-800 fixed top-0 left-0 pt-[72px] z-40">
      <div className="mb-6">
        {/* Navigation header space - text removed */}
      </div>

      <nav className="space-y-1">
        {pathname !== '/projects' && (
          <NavLink
            href="/projects"
            className="flex items-center px-3 py-2 text-[0.99rem] font-medium rounded-md text-gray-800 border border-gray-200 mb-4 hover:bg-[#f9fdff] transition-colors"
          >
            <span className="mr-3 text-[#0271c3]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m12 19-7-7 7-7"></path>
                <path d="M19 12H5"></path>
              </svg>
            </span>
            Back to Projects
          </NavLink>
        )}
        {navItems.map((item) => {
          // Special handling for Project Overview to make it active when viewing a project
          const isActive = item.name === 'Project Overview'
            ? pathname === item.href || (currentProjectId && pathname.startsWith(`/projects/${currentProjectId}`))
            : pathname === item.href;

          return (
            <NavLink
              key={item.name}
              href={item.href}
              className={`flex items-center px-3 py-2 pr-1 text-[0.99rem] font-medium rounded-md transition-colors ${
                isActive
                  ? 'bg-[#f9fdff] text-[#0271c3]'
                  : 'text-gray-700 hover:bg-[#f9fdff] hover:text-[#0271c3]'
              }`}
            >
              <span className={`mr-2 transform scale-110 ${isActive ? 'text-[#0271c3]' : 'text-gray-500'}`}>{item.icon}</span>
              {item.name}
            </NavLink>
          );
        })}
      </nav>
    </div>
  );
}

// Icon components
function HomeIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
      <polyline points="9 22 9 12 15 12 15 22"></polyline>
    </svg>
  );
}

function DocumentIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
      <polyline points="14 2 14 8 20 8"></polyline>
    </svg>
  );
}

function PhotoIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
      <circle cx="9" cy="9" r="2"></circle>
      <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
    </svg>
  );
}

function TodoIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
      <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
      <path d="m9 14 2 2 4-4"></path>
    </svg>
  );
}

function ContactIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
      <circle cx="12" cy="7" r="4"></circle>
    </svg>
  );
}

function NoteIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M12 11h4"></path>
      <path d="M12 15h4"></path>
      <path d="M8 11h.01"></path>
      <path d="M8 15h.01"></path>
      <rect width="18" height="18" x="3" y="3" rx="2"></rect>
    </svg>
  );
}

