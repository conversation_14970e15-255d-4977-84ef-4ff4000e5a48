import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// This route handles the callback from Supabase Auth (email confirmations, OAuth redirects, etc.)
export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');

  if (code) {
    // Exchange the code for a session
    await supabase.auth.exchangeCodeForSession(code);
  }

  // Redirect to the projects page with cache control headers to prevent caching issues
  const response = NextResponse.redirect(new URL('/projects', request.url));

  // Add cache control headers to prevent caching issues
  response.headers.set('Cache-Control', 'no-store, max-age=0');
  response.headers.set('Pragma', 'no-cache');

  return response;
}
