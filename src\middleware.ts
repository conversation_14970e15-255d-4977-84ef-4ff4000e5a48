import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This middleware runs on every request
export async function middleware(request: NextRequest) {
  // AUTHENTICATION BYPASS ENABLED
  console.log('Authentication bypass enabled - all routes accessible');

  // If trying to access an auth route, redirect to projects
  if (request.nextUrl.pathname.startsWith('/auth/')) {
    // Redirect directly to projects page
    const redirectUrl = new URL('/projects', request.url);
    return NextResponse.redirect(redirectUrl);
  }

  // Allow access to all routes
  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    // Protected routes
    '/projects/:path*',
    '/documents/:path*',
    '/photos/:path*',
    '/contacts/:path*',
    '/notes/:path*',
    // Auth routes
    '/auth/signin',
    '/auth/signup',
    '/auth/reset-password',
    // Debug route
    '/auth-debug',
  ],
};
