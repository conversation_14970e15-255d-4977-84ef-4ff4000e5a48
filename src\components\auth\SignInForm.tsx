'use client';

import { useState, useEffect } from 'react';
import { signIn, signUp } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export function SignInForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  // Magic link option removed
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [showDebug, setShowDebug] = useState(false);
  const router = useRouter();

  // Check Supabase connection on component mount
  useEffect(() => {
    async function checkConnection() {
      try {
        const { data, error } = await supabase.from('projects').select('count', { count: 'exact', head: true });

        if (error) {
          setDebugInfo(prev => prev + `\nSupabase connection error: ${error.message}`);
        } else {
          setDebugInfo(prev => prev + '\nSuccessfully connected to Supabase!');
        }

        // Check for existing session
        const { data: sessionData } = await supabase.auth.getSession();
        if (sessionData.session) {
          setDebugInfo(prev => prev + `\nExisting session found for user: ${sessionData.session.user.id}`);
        } else {
          setDebugInfo(prev => prev + '\nNo existing session found.');
        }
      } catch (err) {
        setDebugInfo(prev => prev + `\nUnexpected error: ${err instanceof Error ? err.message : String(err)}`);
      }
    }

    checkConnection();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    setDebugInfo(prev => prev + '\n\n--- Sign In Attempt ---');
    setDebugInfo(prev => prev + `\nAttempting to sign in with email: ${email}`);

    try {
      // Sign in with email and password
      setDebugInfo(prev => prev + '\nUsing email/password authentication');
      const data = await signIn(email, password);
      setDebugInfo(prev => prev + '\nSign in successful!');
      setDebugInfo(prev => prev + `\nUser ID: ${data.user?.id}`);
      setDebugInfo(prev => prev + `\nSession: ${data.session ? 'Created' : 'Not created'}`);

      // Check if session was created
      const { data: sessionData } = await supabase.auth.getSession();
      if (sessionData.session) {
        setDebugInfo(prev => prev + `\nSession verified: ${sessionData.session.user.id}`);
        setSuccess('Sign in successful! Redirecting...');

        // Use multiple redirection methods to ensure it works
        setDebugInfo(prev => prev + '\nAttempting redirection to /projects');

        // Method 1: Next.js router
        router.push('/projects');

        // Method 2: Direct browser navigation (more reliable)
        setTimeout(() => {
          setDebugInfo(prev => prev + '\nFallback: Using window.location for redirection');
          window.location.href = '/projects';
        }, 500);
      } else {
        setDebugInfo(prev => prev + '\nWarning: Session not found after sign in');
        setError('Sign in succeeded but no session was created. Please try again.');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred during sign in';
      setError(errorMessage);
      setDebugInfo(prev => prev + `\nSign in error: ${errorMessage}`);
      console.error('Sign in error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTestUser = async () => {
    if (!email || !password) {
      setError('Please enter both email and password to create a test user');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccess(null);
    setDebugInfo(prev => prev + '\n\n--- Create Test User Attempt ---');
    setDebugInfo(prev => prev + `\nAttempting to create user with email: ${email}`);

    try {
      const data = await signUp(email, password);
      setDebugInfo(prev => prev + '\nUser creation successful!');
      setDebugInfo(prev => prev + `\nUser ID: ${data.user?.id}`);
      setDebugInfo(prev => prev + `\nEmail confirmed: ${data.user?.email_confirmed_at ? 'Yes' : 'No'}`);
      setSuccess('Test user created successfully! You can now sign in.');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred during user creation';
      setError(errorMessage);
      setDebugInfo(prev => prev + `\nUser creation error: ${errorMessage}`);
      console.error('User creation error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-md">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-[#0271c3]">Sign In</h1>
        <p className="mt-2 text-gray-600">Sign in to your account to continue</p>
      </div>

      {error && (
        <div className="p-3 text-sm text-red-800 bg-red-100 rounded-md">
          {error}
        </div>
      )}

      {success && (
        <div className="p-3 text-sm text-green-800 bg-green-100 rounded-md">
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={isLoading}
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password">Password</Label>
            <Link
              href="/auth/reset-password"
              className="text-sm text-[#0271c3] hover:underline"
            >
              Forgot password?
            </Link>
          </div>
          <Input
            id="password"
            type="password"
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            disabled={isLoading}
          />
        </div>

        <div className="flex space-x-3">
          <Button
            type="submit"
            className="flex-1 bg-[#0271c3] hover:bg-[#0260a8]"
            disabled={isLoading}
          >
            {isLoading ? 'Processing...' : 'Sign In'}
          </Button>

          <Button
            type="button"
            variant="outline"
            className="flex-1"
            onClick={handleCreateTestUser}
            disabled={isLoading}
          >
            Create Test User
          </Button>
        </div>
      </form>

      {/* Sign-up link removed as requested */}

      <div className="mt-4 pt-4 border-t border-gray-200">
        <Button
          type="button"
          variant="ghost"
          className="text-xs text-gray-500 w-full"
          onClick={() => setShowDebug(!showDebug)}
        >
          {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
        </Button>

        {showDebug && (
          <div className="mt-2 p-3 bg-gray-100 rounded-md text-xs font-mono whitespace-pre-wrap text-gray-700 max-h-60 overflow-y-auto">
            {debugInfo || 'No debug information available yet.'}
          </div>
        )}
      </div>
    </div>
  );
}
