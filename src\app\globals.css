@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-sans);
  --font-heading: var(--font-heading);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: #f1f5f9;
  --foreground: #0a2540;
  --card: #ffffff;
  --card-foreground: #0a2540;
  --popover: #ffffff;
  --popover-foreground: #0a2540;
  --primary: #0271c3;
  --primary-foreground: #ffffff;
  --secondary: #e6f4fb;
  --secondary-foreground: #0a2540;
  --muted: #e6f4fb;
  --muted-foreground: #5b7a9d;
  --accent: #60a5fa;
  --accent-foreground: #0a2540;
  --destructive: #e63946;
  --border: #cbd5e1;
  --input: #cbd5e1;
  --ring: #0271c3;
  --chart-1: #0271c3;
  --chart-2: #00a8e8;
  --chart-3: #61c9ff;
  --chart-4: #0d41e1;
  --chart-5: #003459;
  --sidebar: #003459;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #0271c3;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #00a8e8;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #004d74;
  --sidebar-ring: #0271c3;
}

.dark {
  --background: #0a2540;
  --foreground: #f5f9fc;
  --card: #003459;
  --card-foreground: #f5f9fc;
  --popover: #003459;
  --popover-foreground: #f5f9fc;
  --primary: #0271c3;
  --primary-foreground: #ffffff;
  --secondary: #004d74;
  --secondary-foreground: #f5f9fc;
  --muted: #004d74;
  --muted-foreground: #a0c8e0;
  --accent: #00a8e8;
  --accent-foreground: #f5f9fc;
  --destructive: #e63946;
  --border: #004d74;
  --input: #004d74;
  --ring: #00a8e8;
  --chart-1: #0271c3;
  --chart-2: #00a8e8;
  --chart-3: #61c9ff;
  --chart-4: #0d41e1;
  --chart-5: #003459;
  --sidebar: #001f33;
  --sidebar-foreground: #f5f9fc;
  --sidebar-primary: #0271c3;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #00a8e8;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #004d74;
  --sidebar-ring: #0271c3;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-sans);
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
  }
  h1 {
    font-weight: 800;
  }
  .card-title {
    font-family: var(--font-heading);
    font-weight: 600;
  }
  .font-bold {
    font-weight: 700;
  }
  .font-semibold {
    font-weight: 600;
  }
  .font-medium {
    font-weight: 500;
  }
}
