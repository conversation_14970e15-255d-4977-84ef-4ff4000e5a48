'use client';

import { useState } from 'react';
import { NavSidebar } from '@/components/nav-sidebar';
import { AppHeader } from '@/components/app-header';
import { NewProjectButton } from '@/components/new-project-button';
import { FileUpload } from '@/components/file-upload';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Search, Plus, Edit, Trash2, Calendar, Tag, FileText } from 'lucide-react';

export default function NotesPage() {
  // Sample notes data
  const [notes, setNotes] = useState([
    {
      id: 1,
      title: 'Meeting with Contractor',
      content: 'Discussed timeline adjustments for the foundation work. Need to follow up on material delivery dates.',
      date: '2025-01-15',
      tags: ['Meeting', 'Important']
    },
    {
      id: 2,
      title: 'Budget Revision',
      content: 'Need to revise the budget for electrical work. Costs have increased by approximately 15% due to material shortages.',
      date: '2025-01-10',
      tags: ['Budget', 'Urgent']
    },
    {
      id: 3,
      title: 'Design Changes',
      content: 'Client requested changes to the kitchen layout. Need to update blueprints and get approval before proceeding.',
      date: '2025-01-05',
      tags: ['Design', 'Client Request']
    },
    {
      id: 4,
      title: 'Permit Application Status',
      content: 'Building permit is still pending approval. Expected to be approved by end of week.',
      date: '2024-12-20',
      tags: ['Permits', 'Follow-up']
    },
    {
      id: 5,
      title: 'Subcontractor Meeting',
      content: 'Scheduled meeting with plumbing and electrical subcontractors for next Tuesday at 10am to coordinate installation schedule.',
      date: '2025-01-12',
      tags: ['Meeting', 'Scheduling']
    },
  ]);

  const [searchTerm, setSearchTerm] = useState('');

  // Handle note import
  const handleNoteImport = (files: File[]) => {
    // In a real app, this would parse text files
    // For this demo, we'll just create notes based on the file names

    const newNotes = files.map((file, index) => {
      // Generate a unique ID for the new note
      const newId = Math.max(...notes.map(note => note.id)) + index + 1;

      // Create a title from the file name
      const title = file.name.split('.')[0].replace(/_/g, ' ');

      // Generate random tags
      const tagOptions = ['Important', 'Follow-up', 'Client Request', 'Budget', 'Design', 'Meeting', 'Scheduling', 'Permits'];
      const numTags = 1 + Math.floor(Math.random() * 3); // 1-3 tags
      const selectedTags: string[] = [];

      for (let i = 0; i < numTags; i++) {
        const randomTag = tagOptions[Math.floor(Math.random() * tagOptions.length)];
        if (!selectedTags.includes(randomTag)) {
          selectedTags.push(randomTag);
        }
      }

      return {
        id: newId,
        title: title,
        content: `Imported note from ${file.name}. This is a placeholder content for the imported note.`,
        date: new Date().toISOString().split('T')[0],
        tags: selectedTags
      };
    });

    setNotes([...notes, ...newNotes]);

    // Show a success message
    alert(`Successfully imported ${files.length} note${files.length > 1 ? 's' : ''}`);
  };

  // Filter notes based on search term
  const filteredNotes = notes.filter(note =>
    note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    note.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    note.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="flex flex-col h-screen">
      <AppHeader />
      <div className="flex">
        <NavSidebar />
        <div className="ml-60 mt-[72px] p-8 pb-24 w-full h-screen overflow-y-auto">
          <div className="mb-6 flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-blue-700">Private Notes</h1>
              <p className="text-gray-600 mt-1">Manage your personal project notes</p>
            </div>
            <div>
              <NewProjectButton />
            </div>
          </div>

          <div className="mb-6 flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search notes..."
                className="pl-9 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <FileUpload
                onUpload={handleNoteImport}
                acceptedFileTypes=".txt,.md"
                multiple={true}
                maxFiles={10}
                maxSize={1}
                buttonText="Import Notes"
                buttonIcon={<FileText className="h-4 w-4 mr-1" />}
                buttonVariant="outline"
                buttonClassName="text-blue-600 border-blue-600"
              />
              <Button className="bg-blue-600 hover:bg-blue-700 flex-shrink-0">
                <Plus className="h-4 w-4 mr-1" />
                New Note
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredNotes.map((note) => (
              <Card key={note.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <div className="bg-blue-50 p-3 border-b">
                    <div className="flex items-start justify-between">
                      <h3 className="font-semibold text-blue-800 text-sm">{note.title}</h3>
                      <div className="flex space-x-1">
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-md">
                          <Edit className="h-3.5 w-3.5 text-blue-600" />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0 rounded-md">
                          <Trash2 className="h-3.5 w-3.5 text-red-500" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="p-3">
                    <p className="text-xs text-gray-700 mb-3 line-clamp-3">{note.content}</p>
                    <div className="flex items-center text-xs text-gray-500 mb-2">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>{note.date}</span>
                    </div>
                    <div className="flex flex-wrap gap-1.5">
                      {note.tags.map((tag, index) => (
                        <div key={index} className="flex items-center bg-gray-100 text-gray-700 rounded-md px-1.5 py-0.5 text-xs">
                          <Tag className="h-2.5 w-2.5 mr-1" />
                          {tag}
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
