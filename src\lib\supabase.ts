import { createClient } from '@supabase/supabase-js';

// Hardcoded values from the project configuration
const supabaseUrl = 'https://twgmcxapetcthhrcvtiw.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InR3Z21jeGFwZXRjdGhocmN2dGl3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzMTg3NjksImV4cCI6MjA2MTg5NDc2OX0.C6trqtzWAOHZxWJybuEfymVq29v8GoAHuE94hrzu8Ns';

// Create a single supabase client for interacting with your database
// Removed logging for performance
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
  // Optimized for performance
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'X-Client-Info': 'project-management-app',
    },
  },
});
