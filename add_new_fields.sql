-- Add new fields to the projects table
-- This script adds the Works Over and Sale Price fields

-- Add the new columns to the projects table
ALTER TABLE projects 
  ADD COLUMN IF NOT EXISTS works_over TEXT DEFAULT 'Not Applicable',
  ADD COLUMN IF NOT EXISTS works_over_number TEXT,
  ADD COLUMN IF NOT EXISTS sale_price TEXT;

-- Add comments to document the new columns
COMMENT ON COLUMN projects.works_over IS 'Indicates if works over regulations apply (Applicable/Not Applicable)';
COMMENT ON COLUMN projects.works_over_number IS 'Works over number when applicable';
COMMENT ON COLUMN projects.sale_price IS 'Sale price of the project in dollars';

-- Display confirmation message
SELECT 'New fields added successfully to projects table' AS status;
