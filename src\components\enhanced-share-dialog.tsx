'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Share2, 
  Link, 
  Mail, 
  Copy, 
  Calendar, 
  Shield, 
  Users, 
  Eye,
  Download,
  Edit,
  Trash2,
  Plus,
  X
} from 'lucide-react';
import { toast } from 'sonner';

interface ShareableDocument {
  id: string;
  name: string;
  type: string;
  size?: string;
}

interface SharePermission {
  id: string;
  email: string;
  name: string;
  permission: 'view' | 'edit' | 'download';
  addedDate: string;
}

interface EnhancedShareDialogProps {
  isOpen: boolean;
  onClose: () => void;
  documents: ShareableDocument[];
}

export function EnhancedShareDialog({ isOpen, onClose, documents }: EnhancedShareDialogProps) {
  const [activeTab, setActiveTab] = useState('link');
  const [linkExpiry, setLinkExpiry] = useState('never');
  const [requirePassword, setRequirePassword] = useState(false);
  const [password, setPassword] = useState('');
  const [allowDownload, setAllowDownload] = useState(true);
  const [shareMessage, setShareMessage] = useState('');
  const [emailRecipients, setEmailRecipients] = useState<string[]>([]);
  const [newEmail, setNewEmail] = useState('');
  const [sharedWith, setSharedWith] = useState<SharePermission[]>([
    {
      id: '1',
      email: '<EMAIL>',
      name: 'John Smith',
      permission: 'view',
      addedDate: '2024-01-15'
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'Sarah Johnson',
      permission: 'edit',
      addedDate: '2024-01-14'
    }
  ]);

  const generateShareLink = () => {
    const baseUrl = window.location.origin;
    const documentIds = documents.map(doc => doc.id).join(',');
    const params = new URLSearchParams({
      docs: documentIds,
      ...(linkExpiry !== 'never' && { expires: linkExpiry }),
      ...(requirePassword && { protected: 'true' }),
      ...(allowDownload && { download: 'true' })
    });
    
    return `${baseUrl}/shared?${params.toString()}`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Link copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy link');
    }
  };

  const addEmailRecipient = () => {
    if (newEmail && !emailRecipients.includes(newEmail)) {
      setEmailRecipients([...emailRecipients, newEmail]);
      setNewEmail('');
    }
  };

  const removeEmailRecipient = (email: string) => {
    setEmailRecipients(emailRecipients.filter(e => e !== email));
  };

  const sendEmailInvites = () => {
    // In a real app, this would send actual emails
    toast.success(`Invitations sent to ${emailRecipients.length} recipient(s)`);
    setEmailRecipients([]);
    setShareMessage('');
  };

  const updatePermission = (userId: string, newPermission: 'view' | 'edit' | 'download') => {
    setSharedWith(prev => 
      prev.map(user => 
        user.id === userId ? { ...user, permission: newPermission } : user
      )
    );
  };

  const removeAccess = (userId: string) => {
    setSharedWith(prev => prev.filter(user => user.id !== userId));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share Documents
          </DialogTitle>
        </DialogHeader>

        {/* Document List */}
        <div className="mb-4">
          <Label className="text-sm font-medium">Sharing {documents.length} document(s):</Label>
          <div className="mt-2 space-y-1">
            {documents.map((doc) => (
              <div key={doc.id} className="flex items-center gap-2 text-sm text-gray-600">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>{doc.name}</span>
                <Badge variant="secondary" className="text-xs">{doc.type}</Badge>
              </div>
            ))}
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="link" className="flex items-center gap-2">
              <Link className="h-4 w-4" />
              Share Link
            </TabsTrigger>
            <TabsTrigger value="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Email
            </TabsTrigger>
            <TabsTrigger value="manage" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Manage Access
            </TabsTrigger>
          </TabsList>

          {/* Share Link Tab */}
          <TabsContent value="link" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label>Link Expiry</Label>
                <Select value={linkExpiry} onValueChange={setLinkExpiry}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="never">Never expires</SelectItem>
                    <SelectItem value="1day">1 day</SelectItem>
                    <SelectItem value="1week">1 week</SelectItem>
                    <SelectItem value="1month">1 month</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <Label>Require password</Label>
                <Switch checked={requirePassword} onCheckedChange={setRequirePassword} />
              </div>

              {requirePassword && (
                <div>
                  <Label>Password</Label>
                  <Input
                    type="password"
                    placeholder="Enter password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label>Allow downloads</Label>
                <Switch checked={allowDownload} onCheckedChange={setAllowDownload} />
              </div>

              <div className="p-3 bg-gray-50 rounded-md">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-mono break-all">{generateShareLink()}</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generateShareLink())}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Email Tab */}
          <TabsContent value="email" className="space-y-4">
            <div>
              <Label>Add recipients</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  type="email"
                  placeholder="Enter email address"
                  value={newEmail}
                  onChange={(e) => setNewEmail(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && addEmailRecipient()}
                />
                <Button onClick={addEmailRecipient} disabled={!newEmail}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {emailRecipients.length > 0 && (
              <div>
                <Label>Recipients</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {emailRecipients.map((email) => (
                    <Badge key={email} variant="secondary" className="flex items-center gap-1">
                      {email}
                      <button onClick={() => removeEmailRecipient(email)}>
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            <div>
              <Label>Message (optional)</Label>
              <Textarea
                placeholder="Add a personal message..."
                value={shareMessage}
                onChange={(e) => setShareMessage(e.target.value)}
                rows={3}
              />
            </div>

            <Button 
              onClick={sendEmailInvites} 
              disabled={emailRecipients.length === 0}
              className="w-full"
            >
              Send Invitations
            </Button>
          </TabsContent>

          {/* Manage Access Tab */}
          <TabsContent value="manage" className="space-y-4">
            <div>
              <Label className="text-sm font-medium">People with access</Label>
              <div className="mt-2 space-y-2">
                {sharedWith.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-3 border rounded-md">
                    <div>
                      <div className="font-medium text-sm">{user.name}</div>
                      <div className="text-xs text-gray-500">{user.email}</div>
                      <div className="text-xs text-gray-400">Added {user.addedDate}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select
                        value={user.permission}
                        onValueChange={(value: 'view' | 'edit' | 'download') => 
                          updatePermission(user.id, value)
                        }
                      >
                        <SelectTrigger className="w-24">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="view">
                            <div className="flex items-center gap-2">
                              <Eye className="h-3 w-3" />
                              View
                            </div>
                          </SelectItem>
                          <SelectItem value="edit">
                            <div className="flex items-center gap-2">
                              <Edit className="h-3 w-3" />
                              Edit
                            </div>
                          </SelectItem>
                          <SelectItem value="download">
                            <div className="flex items-center gap-2">
                              <Download className="h-3 w-3" />
                              Download
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeAccess(user.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
