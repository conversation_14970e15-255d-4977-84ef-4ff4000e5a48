import { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export function BuildingCompliance() {
  // State for editable fields
  const [buildingConsent, setBuildingConsent] = useState('');
  const [resourceConsent, setResourceConsent] = useState('');
  const [topoStart, setTopoStart] = useState('');
  const [topoCompleted, setTopoCompleted] = useState('');
  const [epa, setEpa] = useState('');
  const [worksOver, setWorksOver] = useState('Not Applicable');
  const [worksOverNumber, setWorksOverNumber] = useState('');

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden mt-6 mb-12">
      <div className="w-full bg-[#e1eeff] py-2 px-6 -mt-[3px]">
        <h2 className="text-xl font-semibold">Building & Compliance</h2>
      </div>
      <div className="p-6 pt-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="building-consent" className="block text-sm font-medium mb-1 text-gray-700">
              Building Consent #
            </Label>
            <Input
              id="building-consent"
              value={buildingConsent}
              onChange={(e) => setBuildingConsent(e.target.value)}
              placeholder="Enter building consent number"
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="resource-consent" className="block text-sm font-medium mb-1 text-gray-700">
              Resource Consent #
            </Label>
            <Input
              id="resource-consent"
              value={resourceConsent}
              onChange={(e) => setResourceConsent(e.target.value)}
              placeholder="Enter resource consent number"
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="topo-start" className="block text-sm font-medium mb-1 text-gray-700">
              Topo Start
            </Label>
            <Input
              id="topo-start"
              type="date"
              value={topoStart}
              onChange={(e) => setTopoStart(e.target.value)}
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="topo-completed" className="block text-sm font-medium mb-1 text-gray-700">
              Topo Completed
            </Label>
            <Input
              id="topo-completed"
              type="date"
              value={topoCompleted}
              onChange={(e) => setTopoCompleted(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="md:col-span-2">
            <Label htmlFor="epa" className="block text-sm font-medium mb-1 text-gray-700">
              EPA
            </Label>
            <Input
              id="epa"
              value={epa}
              onChange={(e) => setEpa(e.target.value)}
              placeholder="Enter EPA information"
              className="w-full"
            />
          </div>
          <div>
            <Label htmlFor="works-over" className="block text-sm font-medium mb-1 text-gray-700">
              Works Over
            </Label>
            <Select value={worksOver} onValueChange={setWorksOver}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Not Applicable">Not Applicable</SelectItem>
                <SelectItem value="Applicable">Applicable</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {worksOver === 'Applicable' && (
            <div>
              <Label htmlFor="works-over-number" className="block text-sm font-medium mb-1 text-gray-700">
                Works Over Number
              </Label>
              <Input
                id="works-over-number"
                value={worksOverNumber}
                onChange={(e) => setWorksOverNumber(e.target.value)}
                placeholder="Enter works over number"
                className="w-full"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
